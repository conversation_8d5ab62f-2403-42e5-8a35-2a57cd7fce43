# 🎨 DIAGRAMMES UML PROFESSIONNELS - DIMACLEAN

## 1. DIAGRAMME DE CAS D'USAGE

```plantuml
@startuml DimaClean_UseCases
!theme aws-orange

left to right direction

actor "<PERSON><PERSON><PERSON><PERSON><PERSON>\nStandard" as User
actor "Admini<PERSON><PERSON><PERSON>" as Admin

rectangle "DimaClean System" {
  
  package "Gestion des Fichiers" {
    usecase "Importer CSV" as UC1
    usecase "Prévisualiser\nDonnées" as UC2
    usecase "Gérer Fichiers" as UC3
  }
  
  package "Traitement des Données" {
    usecase "Nettoyer\nDonnées" as UC4
    usecase "Transformer\nDonnées" as UC5
    usecase "Calculer\nStatistiques" as UC6
  }
  
  package "Visualisation" {
    usecase "Créer\nGraphiques" as UC7
    usecase "Personnaliser\nDashboard" as UC8
    usecase "Exporter\nVisualisations" as UC9
  }
  
  package "Export et Rapports" {
    usecase "Exporter CSV\nNettoyé" as UC10
    usecase "Générer\nRapport PDF" as UC11
  }
  
  package "Administration" {
    usecase "Gérer\nUtilisateurs" as UC12
    usecase "Superviser\nSystème" as UC13
    usecase "Consulter\nLogs" as UC14
  }
  
  package "Authentification" {
    usecase "Se Connecter" as UC15
    usecase "Gérer Profil" as UC16
  }
}

' Relations Utilisateur Standard
User --> UC1
User --> UC2
User --> UC3
User --> UC4
User --> UC5
User --> UC6
User --> UC7
User --> UC8
User --> UC9
User --> UC10
User --> UC11
User --> UC15
User --> UC16

' Relations Administrateur
Admin --> UC12
Admin --> UC13
Admin --> UC14
Admin --> UC15
Admin --> UC16

' Extensions et Inclusions
UC1 ..> UC2 : <<include>>
UC4 ..> UC6 : <<include>>
UC7 ..> UC8 : <<extend>>
UC11 ..> UC6 : <<include>>

' Notes
note right of UC4
  Nettoyage automatique :
  - Suppression doublons
  - Gestion valeurs manquantes
  - Normalisation formats
end note

note right of UC7
  Types de graphiques :
  - Histogrammes
  - Courbes
  - Camemberts
  - Box plots
end note

@enduml
```

## 2. DIAGRAMME DE CLASSES DÉTAILLÉ

```plantuml
@startuml DimaClean_Classes
!theme aws-orange

package "Models" {
  
  class User {
    +id: UUID
    +email: String
    +password_hash: String
    +first_name: String
    +last_name: String
    +role: UserRole
    +created_at: DateTime
    +is_active: Boolean
    --
    +authenticate(password): Boolean
    +change_password(new_password): Boolean
    +get_datasets(): List<DataSet>
  }
  
  enum UserRole {
    ADMIN
    USER
    VIEWER
  }
  
  class DataSet {
    +id: UUID
    +user_id: UUID
    +filename: String
    +original_filename: String
    +file_path: String
    +file_hash: String
    +file_size: Integer
    +rows_count: Integer
    +columns_count: Integer
    +schema: JSON
    +metadata: JSON
    +status: DataSetStatus
    +created_at: DateTime
    +updated_at: DateTime
    --
    +validate_file(): Boolean
    +analyze_quality(): QualityReport
    +get_preview(limit): DataFrame
    +export_csv(): String
  }
  
  enum DataSetStatus {
    UPLOADED
    ANALYZING
    READY
    CLEANING
    CLEANED
    ERROR
  }
  
  class CleaningJob {
    +id: UUID
    +dataset_id: UUID
    +configuration: JSON
    +status: JobStatus
    +progress: Float
    +started_at: DateTime
    +completed_at: DateTime
    +error_message: String
    --
    +execute(): CleaningResult
    +get_progress(): Float
    +cancel(): Boolean
  }
  
  enum JobStatus {
    PENDING
    RUNNING
    COMPLETED
    FAILED
    CANCELLED
  }
  
  class CleaningOperation {
    +id: UUID
    +job_id: UUID
    +operation_type: OperationType
    +column_name: String
    +parameters: JSON
    +results: JSON
    +affected_rows: Integer
    +execution_time: Float
    +executed_at: DateTime
    --
    +execute(dataframe): DataFrame
    +validate_parameters(): Boolean
    +get_summary(): String
  }
  
  enum OperationType {
    REMOVE_DUPLICATES
    HANDLE_MISSING_VALUES
    NORMALIZE_TEXT
    DETECT_OUTLIERS
    TRANSFORM_COLUMN
    FILTER_ROWS
  }
  
  class Visualization {
    +id: UUID
    +dataset_id: UUID
    +user_id: UUID
    +title: String
    +chart_type: ChartType
    +configuration: JSON
    +created_at: DateTime
    --
    +render(): ChartData
    +export_image(format): ByteArray
    +clone(): Visualization
  }
  
  enum ChartType {
    HISTOGRAM
    LINE_CHART
    PIE_CHART
    SCATTER_PLOT
    BOX_PLOT
    HEATMAP
  }
  
  class Dashboard {
    +id: UUID
    +user_id: UUID
    +name: String
    +layout: JSON
    +is_public: Boolean
    +created_at: DateTime
    +updated_at: DateTime
    --
    +add_visualization(viz): Boolean
    +remove_visualization(viz_id): Boolean
    +export_pdf(): ByteArray
  }
  
  class AuditLog {
    +id: UUID
    +user_id: UUID
    +action: String
    +resource_type: String
    +resource_id: UUID
    +changes: JSON
    +ip_address: String
    +user_agent: String
    +timestamp: DateTime
    --
    +log_action(action, resource): Boolean
  }
}

package "Services" {
  
  abstract class BaseService {
    #logger: Logger
    --
    #handle_error(error): ServiceResult
    #success_result(data): ServiceResult
  }
  
  class DataProcessingService {
    -cleaning_engine: CleaningEngine
    -analyzer: DataAnalyzer
    -cache_timeout: Integer
    --
    +process_uploaded_file(file): ServiceResult
    +clean_dataset(dataset_id, config): ServiceResult
    +get_statistics(dataset_id): ServiceResult
    +export_dataset(dataset_id, format): ServiceResult
    -validate_file(file): Boolean
    -cache_dataframe(dataset_id, df): Void
  }
  
  class AuthenticationService {
    -jwt_secret: String
    -token_expiry: Integer
    --
    +authenticate(email, password): ServiceResult
    +generate_token(user): String
    +validate_token(token): User
    +refresh_token(token): String
    +logout(token): Boolean
  }
  
  class VisualizationService {
    -chart_factory: ChartFactory
    --
    +create_chart(dataset_id, config): ServiceResult
    +update_chart(chart_id, config): ServiceResult
    +export_chart(chart_id, format): ServiceResult
    +get_chart_data(chart_id): ServiceResult
  }
  
  class ReportService {
    -pdf_generator: PDFGenerator
    -template_engine: TemplateEngine
    --
    +generate_cleaning_report(job_id): ServiceResult
    +generate_dashboard_report(dashboard_id): ServiceResult
    +export_dataset_summary(dataset_id): ServiceResult
  }
}

package "Strategies" {
  
  interface CleaningStrategy {
    +execute(dataframe, config): CleaningResult
    +validate_config(config): Boolean
  }
  
  class DuplicateRemovalStrategy {
    +execute(dataframe, config): CleaningResult
    +validate_config(config): Boolean
    -detect_duplicates(df): Series
  }
  
  class MissingValueStrategy {
    +execute(dataframe, config): CleaningResult
    +validate_config(config): Boolean
    -determine_strategy(series): String
    -apply_strategy(series, strategy): Any
  }
  
  class OutlierDetectionStrategy {
    +execute(dataframe, config): CleaningResult
    +validate_config(config): Boolean
    -detect_outliers(series, method): Series
    -cap_outliers(df, column, mask): DataFrame
  }
  
  class CleaningEngine {
    -strategies: Map<String, CleaningStrategy>
    --
    +clean_data(dataframe, config): CleaningResult
    +register_strategy(name, strategy): Void
    +get_available_strategies(): List<String>
  }
}

' Relations
User ||--o{ DataSet : owns
User ||--o{ Dashboard : creates
User ||--o{ Visualization : creates
User ||--o{ AuditLog : generates

DataSet ||--o{ CleaningJob : processes
DataSet ||--o{ Visualization : visualizes

CleaningJob ||--o{ CleaningOperation : contains

Dashboard ||--o{ Visualization : displays

BaseService <|-- DataProcessingService
BaseService <|-- AuthenticationService
BaseService <|-- VisualizationService
BaseService <|-- ReportService

CleaningStrategy <|.. DuplicateRemovalStrategy
CleaningStrategy <|.. MissingValueStrategy
CleaningStrategy <|.. OutlierDetectionStrategy

CleaningEngine o-- CleaningStrategy : uses

DataProcessingService --> CleaningEngine : uses
DataProcessingService --> DataSet : manages
AuthenticationService --> User : authenticates
VisualizationService --> Visualization : manages
ReportService --> DataSet : reports

@enduml
```

## 3. DIAGRAMME DE SÉQUENCE - UPLOAD ET NETTOYAGE

```plantuml
@startuml Upload_Cleaning_Sequence
!theme aws-orange

actor User
participant "React\nFrontend" as Frontend
participant "Django\nAPI" as API
participant "Data Processing\nService" as Service
participant "Cleaning\nEngine" as Engine
participant "Database" as DB
participant "Cache" as Cache

== Phase 1: Upload du fichier ==

User -> Frontend: Sélectionne fichier CSV
activate Frontend

Frontend -> Frontend: Validation côté client\n(taille, format)

Frontend -> API: POST /api/upload/\n(multipart/form-data)
activate API

API -> Service: process_uploaded_file(file)
activate Service

Service -> Service: validate_file(file)
note right: Validation sécurité\net format

Service -> Service: parse_csv_file(file)
note right: Parsing avec gestion\nd'encodage

Service -> Service: analyze_dataset(dataframe)
note right: Analyse qualité\nautomatique

Service -> DB: create_dataset_record()
activate DB
DB --> Service: dataset_id
deactivate DB

Service -> Cache: cache_dataframe(dataset_id, df)
activate Cache
Cache --> Service: success
deactivate Cache

Service --> API: ServiceResult(dataset, analysis, preview)
deactivate Service

API --> Frontend: JSON response\n(dataset info + preview)
deactivate API

Frontend --> User: Affiche aperçu\net statistiques
deactivate Frontend

== Phase 2: Configuration du nettoyage ==

User -> Frontend: Configure options\nde nettoyage
activate Frontend

Frontend -> Frontend: Validation configuration

Frontend -> API: POST /api/datasets/{id}/clean/\n(cleaning_config)
activate API

API -> Service: clean_dataset(dataset_id, config)
activate Service

Service -> DB: get_dataset(dataset_id)
activate DB
DB --> Service: dataset
deactivate DB

Service -> Cache: get_cached_dataframe(dataset_id)
activate Cache
Cache --> Service: dataframe
deactivate Cache

Service -> Engine: clean_data(dataframe, config)
activate Engine

loop Pour chaque opération de nettoyage
    Engine -> Engine: apply_strategy(operation)
    note right: Stratégies:\n- Doublons\n- Valeurs manquantes\n- Outliers
end

Engine --> Service: CleaningResult\n(cleaned_df, operations_log)
deactivate Engine

Service -> DB: save_cleaning_operations(operations)
activate DB
DB --> Service: success
deactivate DB

Service -> DB: update_dataset_status(CLEANED)
activate DB
DB --> Service: success
deactivate DB

Service -> Cache: cache_dataframe(dataset_id, cleaned_df, "_cleaned")
activate Cache
Cache --> Service: success
deactivate Cache

Service --> API: ServiceResult(cleaned_data, summary)
deactivate Service

API --> Frontend: JSON response\n(résultats nettoyage)
deactivate API

Frontend --> User: Affiche données nettoyées\net rapport d'opérations
deactivate Frontend

== Phase 3: Gestion d'erreurs ==

alt Erreur de validation
    Service -> API: ServiceResult(error)
    API -> Frontend: HTTP 400 + error message
    Frontend -> User: Message d'erreur clair
else Erreur de traitement
    Service -> Service: log_error()
    Service -> API: ServiceResult(error)
    API -> Frontend: HTTP 500 + error message
    Frontend -> User: Message d'erreur + support
else Timeout
    Service -> Service: cancel_operation()
    Service -> API: ServiceResult(timeout)
    API -> Frontend: HTTP 408 + timeout message
    Frontend -> User: Option de réessayer
end

@enduml
```

## 4. DIAGRAMME D'ACTIVITÉ - PROCESSUS DE NETTOYAGE

```plantuml
@startuml Cleaning_Activity
!theme aws-orange

start

:Utilisateur upload fichier CSV;

:Validation du fichier;

if (Fichier valide?) then (non)
  :Afficher erreur de validation;
  stop
else (oui)
  :Parser le fichier CSV;
endif

:Analyser la qualité des données;

:Afficher aperçu et statistiques;

:Utilisateur configure le nettoyage;

fork
  :Détecter les doublons;
  if (Doublons trouvés?) then (oui)
    :Marquer les doublons;
  endif
fork again
  :Analyser les valeurs manquantes;
  if (Valeurs manquantes?) then (oui)
    :Déterminer stratégie de remplacement;
  endif
fork again
  :Détecter les outliers;
  if (Outliers détectés?) then (oui)
    :Marquer les outliers;
  endif
end fork

:Appliquer les opérations de nettoyage;

partition "Nettoyage Séquentiel" {
  if (Supprimer doublons?) then (oui)
    :Supprimer les lignes dupliquées;
    :Logger l'opération;
  endif
  
  if (Traiter valeurs manquantes?) then (oui)
    :Appliquer stratégie de remplacement;
    :Logger l'opération;
  endif
  
  if (Traiter outliers?) then (oui)
    :Appliquer traitement des outliers;
    :Logger l'opération;
  endif
  
  if (Normaliser le texte?) then (oui)
    :Normaliser les colonnes texte;
    :Logger l'opération;
  endif
}

:Calculer les statistiques finales;

:Générer le rapport de nettoyage;

:Sauvegarder les données nettoyées;

:Afficher les résultats à l'utilisateur;

if (Utilisateur satisfait?) then (non)
  :Permettre ajustements;
  note right: L'utilisateur peut\nmodifier la configuration\net relancer le nettoyage
  backward :Reconfigurer le nettoyage;
else (oui)
  :Proposer export/visualisation;
endif

stop

@enduml
```

## 5. DIAGRAMME DE DÉPLOIEMENT

```plantuml
@startuml Deployment_Diagram
!theme aws-orange

node "Client Browser" {
  component "React App" as ReactApp
  component "Chart.js" as Charts
  component "Tailwind CSS" as Styles
}

node "Load Balancer" {
  component "Nginx" as LB
}

node "Application Server" {
  component "Django App" as Django
  component "Django REST Framework" as DRF
  component "Celery Workers" as Celery
}

node "Data Processing" {
  component "Pandas Engine" as Pandas
  component "NumPy" as NumPy
  component "Cleaning Strategies" as Strategies
}

node "Cache Layer" {
  database "Redis" as Redis
}

node "Database Server" {
  database "PostgreSQL" as DB
}

node "File Storage" {
  folder "CSV Files" as Files
  folder "Generated Reports" as Reports
}

node "Monitoring" {
  component "Prometheus" as Monitoring
  component "Grafana" as Dashboard
}

' Connections
ReactApp --> LB : HTTPS
Charts --> ReactApp
Styles --> ReactApp

LB --> Django : HTTP
Django --> DRF
Django --> Celery : Async Tasks
DRF --> Pandas : Data Processing
Pandas --> NumPy
Pandas --> Strategies

Django --> Redis : Cache
Django --> DB : ORM
Django --> Files : File I/O
Django --> Reports : PDF Generation

Celery --> Redis : Task Queue
Celery --> Pandas : Heavy Processing

Django --> Monitoring : Metrics
Monitoring --> Dashboard : Visualization

note right of ReactApp
  Single Page Application
  - Modern React 18
  - State management
  - Responsive design
end note

note right of Django
  Backend Services
  - REST API
  - Authentication
  - Business Logic
end note

note right of Pandas
  Data Processing
  - CSV parsing
  - Data cleaning
  - Statistical analysis
end note

@enduml
```

Ces diagrammes UML professionnels couvrent tous les aspects importants de votre projet DimaClean. Ils sont prêts à être intégrés dans votre rapport de stage et votre présentation de soutenance.

Voulez-vous que je génère d'autres types de diagrammes ou que je détaille certains aspects spécifiques ?
