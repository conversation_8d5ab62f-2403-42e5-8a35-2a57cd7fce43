# Generated by Django 5.2.4 on 2025-07-15 20:39

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='DataSet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filename', models.CharField(max_length=255)),
                ('original_filename', models.CharField(max_length=255)),
                ('upload_date', models.DateTimeField(auto_now_add=True)),
                ('file_size', models.IntegerField()),
                ('rows_count', models.IntegerField()),
                ('columns_count', models.IntegerField()),
                ('column_names', models.JSONField()),
                ('data_types', models.JSONField()),
                ('missing_values', models.JSONField()),
                ('is_cleaned', models.BooleanField(default=False)),
                ('cleaning_report', models.JSONField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-upload_date'],
            },
        ),
        migrations.CreateModel(
            name='CleaningOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_type', models.CharField(choices=[('remove_duplicates', 'Suppression des doublons'), ('fill_missing_numeric', 'Remplissage valeurs manquantes numériques'), ('fill_missing_text', 'Remplissage valeurs manquantes texte'), ('normalize_text', 'Normalisation du texte'), ('remove_outliers', 'Suppression des outliers')], max_length=50)),
                ('column_name', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField()),
                ('affected_rows', models.IntegerField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('dataset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operations', to='data_processing.dataset')),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
    ]
